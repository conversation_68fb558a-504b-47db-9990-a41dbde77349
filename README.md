# Simple AWS Lambda Function - JSONPlaceholder API Caller

This simple AWS Lambda function makes HTTP requests to the JSONPlaceholder API (https://jsonplaceholder.typicode.com/) when invoked.

## Function Overview

This Lambda function:
1. Receives an event trigger
2. Makes a GET request to the JSONPlaceholder API endpoint
3. Returns the API response data

## Project Structure

```
project/
├── src/
│   └── handlers/
│       └── lambda_function.py   # Main Lambda handler
├── deployment/                  # Deployment package directory
│   └── lambda_deployment.zip    # Final deployment package
├── requirements.txt             # Python dependencies
└── venv/                        # Virtual environment (not included in deployment)
```

## Requirements

- Python 3.x
- `requests` library

## Deployment Steps

Follow these steps to deploy the Lambda function to AWS:

1. **Set up the environment**:
   ```bash
   # Create a virtual environment
   python -m venv venv
   
   # Activate the virtual environment
   source venv/bin/activate
   
   # Install dependencies
   pip install requests
   ```

2. **Create the deployment package**:
   ```bash
   # Create deployment directory
   mkdir -p deployment/package
   
   # Copy Lambda function to the deployment directory
   cp src/handlers/lambda_function.py deployment/package/
   
   # Install dependencies in the deployment directory
   pip install -t deployment/package requests
   OR
   pip install -t deployment/package -r requirements.txt
   
   # Create the ZIP file from the package directory
   cd deployment/package && zip -r ../lambda_deployment.zip .
   ```

3. **Deploy to AWS Lambda**:
   - Sign in to the AWS Management Console
   - Navigate to the Lambda service
   - Click "Create function"
   - Choose "Author from scratch"
   - Enter a name for your function (e.g., "jsonplaceholder-api-caller")
   - Select "Python 3.9" as the runtime (or latest available)
   - Click "Create function"
   - In the "Code source" section, select "Upload from" > ".zip file"
   - Upload the `deployment/lambda_deployment.zip` file
   - Set the Handler to: `lambda_function.lambda_handler`
   - Click "Save"

4. **Test the function**:
   - Click the "Test" tab
   - Create a new test event with any JSON content (the function doesn't require specific input)
   - Click "Test" to run the function
   - Verify the function returns data from the JSONPlaceholder API

## Function Response

The function returns a JSON object with the following structure:
```json
{
  "statusCode": 200,
  "headers": {
    "Content-Type": "application/json"
  },
  "body": {
    "message": "API call successful",
    "data": [
      {
        "userId": 1,
        "id": 1,
        "title": "...",
        "body": "..."
      },
      ...
    ]
  }
}
```

## Error Handling

If an error occurs during the API call, the function returns:
```json
{
  "statusCode": 500,
  "headers": {
    "Content-Type": "application/json"
  },
  "body": {
    "message": "Error calling API",
    "error": "Error message"
  }
}
``` 