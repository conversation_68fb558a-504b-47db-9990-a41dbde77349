# import os
# import json
# import random
# import string
# import requests
# import csv
# from datetime import datetime
# from pymongo import MongoClient
# from bson.objectid import ObjectId

# # Helper to generate random alphanumeric key
# def generateRandomKey(length):
#     characters = string.ascii_uppercase + string.digits
#     return ''.join(random.choices(characters, k=length))

# # Helper to format a date string or datetime
# def format_date(date_obj, output_format="%m/%d/%Y"):
#     if isinstance(date_obj, datetime):
#         return date_obj.strftime(output_format)
#     try:
#         dt = datetime.fromisoformat(date_obj.replace("Z", ""))
#         return dt.strftime(output_format)
#     except Exception:
#         return date_obj

# # Helper to convert sex text to expected abbreviation
# def convert_sex(sex_value):
#     if not sex_value:
#         return sex_value
#     sex_value = sex_value.strip().lower()
#     if sex_value == "male":
#         return "M"
#     elif sex_value == "female":
#         return "F"
#     return sex_value

# # Mapping for full state names to abbreviations
# STATE_MAP = {
#     "Alabama": "AL", "Alaska": "AK", "Arizona": "AZ", "Arkansas": "AR",
#     "California": "CA", "Colorado": "CO", "Connecticut": "CT", "Delaware": "DE",
#     "Florida": "FL", "Georgia": "GA", "Hawaii": "HI", "Idaho": "ID",
#     "Illinois": "IL", "Indiana": "IN", "Iowa": "IA", "Kansas": "KS",
#     "Kentucky": "KY", "Louisiana": "LA", "Maine": "ME", "Maryland": "MD",
#     "Massachusetts": "MA", "Michigan": "MI", "Minnesota": "MN", "Mississippi": "MS",
#     "Missouri": "MO", "Montana": "MT", "Nebraska": "NE", "Nevada": "NV",
#     "New Hampshire": "NH", "New Jersey": "NJ", "New Mexico": "NM", "New York": "NY",
#     "North Carolina": "NC", "North Dakota": "ND", "Ohio": "OH", "Oklahoma": "OK",
#     "Oregon": "OR", "Pennsylvania": "PA", "Rhode Island": "RI", "South Carolina": "SC",
#     "South Dakota": "SD", "Tennessee": "TN", "Texas": "TX", "Utah": "UT",
#     "Vermont": "VT", "Virginia": "VA", "Washington": "WA", "West Virginia": "WV",
#     "Wisconsin": "WI", "Wyoming": "WY"
# }

# def convert_state(full_state):
#     if not full_state:
#         return full_state
#     return STATE_MAP.get(full_state.strip(), full_state.strip())

# # Helper to create CSV file from a payload dictionary
# def create_csv_from_payload(payload, output_file):
#     with open(output_file, 'w', newline='') as csvfile:
#         fieldnames = payload.keys()
#         writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
#         writer.writeheader()
#         writer.writerow(payload)
#     return output_file

# # Lambda handler
# def lambda_handler(event, context):
#     print("this is the event {}".format(event, context))
#     # Case 1: Event from API Gateway (with 'body')
#     if 'body' in event:
#         try:
#             # Parse JSON from body
#             body = json.loads(event['body'])
            
#             # Check and retrieve meeting_record
#             if 'meeting_record' in body:
#                 meeting_record = body['meeting_record']
#             else:
#                 meeting_record = body
#         except Exception as e:
#             print(f"Error parsing body: {e}")
#             return {
#                 'statusCode': 400,
#                 'body': json.dumps({'message': 'Invalid JSON in request body'})
#             }
    
#     # Case 2: Direct test event (already a dict)
#     elif 'meeting_record' in event:
#         meeting_record = event['meeting_record']
    
#     # Case 3: The entire event is the meeting_record
#     elif all(key in event for key in ['_id', 'serviceProvided', 'therapistId']):
#         meeting_record = event
    
#     else:
#         return {
#             'statusCode': 400,
#             'body': json.dumps({'message': 'No meeting_record found in event'})
#         }

    
#     # Extract basic fields from meeting_record
#     claim_number = meeting_record["_id"]["$oid"]
#     proc_code = meeting_record.get("serviceProvided", "")
#     from_date = format_date(meeting_record["createdAt"]["$date"], "%m-%d-%y")
#     client_oid = meeting_record["clientId"]["$oid"]
#     therapist_oid = meeting_record["therapistId"]["$oid"]
#     client_id = ObjectId(client_oid)
#     therapist_id = ObjectId(therapist_oid)
    
#     # Connect to MongoDB (get details from environment variables)
#     MONGO_URI = os.environ.get('MONGO_URI', 'mongodb://localhost:27017')
#     DB_NAME = os.environ.get('DB_NAME', 'your_database_name')
#     mongo_client = MongoClient(MONGO_URI)
#     db = mongo_client[DB_NAME]
    
#     # Collections
#     users_collection = db['users']
#     insurance_collection = db['insurance']
#     insurancecompanies_collection = db['insurancecompanies']
#     diagnosisnotes_collection = db['diagnosisnotes']
#     postsessionclaims_collection = db['postsessionclaims']
    
#     # Get diagnosis info from diagnosisnotes collection
#     diag_1 = ""
#     diag_2 = ""
#     for record in diagnosisnotes_collection.find({"clientId": client_id}):
#         if record.get("diagnosisICDcodes") and len(record["diagnosisICDcodes"]) > 0:
#             diag_1 = record["diagnosisICDcodes"][0].get("value", "").replace(".", "")
#             if record.get("secondaryDiagnosisICDcodes") and len(record["secondaryDiagnosisICDcodes"]) > 0:
#                 diag_2 = record["secondaryDiagnosisICDcodes"][0].get("value", "").replace(".", "")
#             break
    
#     # Retrieve client and provider records from users
#     client_record = users_collection.find_one({"_id": client_id})
#     provider_record = users_collection.find_one({"_id": therapist_id})
    
#     # Retrieve client's insurance record; use $or to handle differing formats
#     insurance_record = insurance_collection.find_one({
#         "$or": [
#             {"clientId": client_id},
#             {"clientId.$oid": client_oid}
#         ]
#     })
#     if insurance_record:
#         insurance_company_id = insurance_record.get("insuranceCompanyId")
#         insurance_company_record = insurancecompanies_collection.find_one({"_id": insurance_company_id})
#         if insurance_company_record:
#             organization_name = insurance_company_record.get("organizationName", "")
#             trading_partner_service_id = insurance_company_record.get("tradingPartnerServiceId", "")
#         else:
#             organization_name = ""
#             trading_partner_service_id = ""
#             print("No insurance company record found for id:", insurance_company_id)
#     else:
#         organization_name = ""
#         trading_partner_service_id = ""
#         print("No insurance record found for client id:", client_id)
    
#     # Determine place_of_service and modifier based solely on organization_name
#     org_name_norm = organization_name.strip() if organization_name else ""
#     if org_name_norm in ["Alliance Health", "United Health Care Community Plan", "United Health Care Community Plan "]:
#         place_of_service = "11"
#         modifier = ""  # No modifier for these companies
#         procedure_modifiers = [""]
#     else:
#         place_of_service = "10"
#         modifier = "95"
#         procedure_modifiers = ["95"]
    
#     # Generate a random PCN key of length 20
#     pcn_key = generateRandomKey(20)
    
#     # Build the complete claim payload
#     payload = {
#         "claim_form": 1500,
#         "payer_name": organization_name,
#         "payerid": trading_partner_service_id,
#         "accept_assign": "Y",
#         "employment_related": "N",
        
#         # Insured (client) information:
#         "ins_name_f": client_record.get("firstname", "") if client_record else "",
#         "ins_name_l": client_record.get("lastname", "") if client_record else "",
#         "ins_addr_1": client_record.get("streetAddress", "") if client_record else "",
#         "ins_city": client_record.get("city", "") if client_record else "",
#         "ins_dob": format_date(client_record.get("dateOfBirth", "")) if client_record else "",
#         "ins_sex": convert_sex(client_record.get("gender", "")) if client_record else "",
#         "ins_state": convert_state(client_record.get("state", "")) if client_record else "",
#         "ins_zip": client_record.get("zipCode", "") if client_record else "",
#         "ins_number": insurance_record.get("subscriber", {}).get("memberId", "") if insurance_record else "",
        
#         # Billing (provider/facility) information:
#         "bill_taxonomy": "251S00000X",
#         "place_of_service_1": place_of_service,
        
#         # Provider (therapist) information:
#         "prov_name_f": provider_record.get("firstname", "") if provider_record else "",
#         "prov_name_l": provider_record.get("lastname", "") if provider_record else "",
#         "prov_npi": provider_record.get("nPI1", "") if provider_record else "",
#         "prov_taxonomy": provider_record.get("taxonomyCode", "") if provider_record else "",
        
#         # Claim identifiers and charges:
#         "pcn": pcn_key,
#         "charge_1": 180,
#         "pat_rel": "18",
#         "total_charge": 180,
        
#         # Service and diagnosis details:
#         "claimNumber": claim_number,
#         "proc_code_1": proc_code,
#         "mod1_1": modifier,
#         "diag_1": diag_1,
#         "diag_2": diag_2,
#         "from_date_1": from_date,
        
#         # Billing entity details:
#         "bill_name": "Lavni Inc",
#         "bill_addr_1": "804 South Garnett St.",
#         "bill_addr_2": "",
#         "bill_city": "Henderson",
#         "bill_state": "NC",  # Assume billing details are already abbreviated
#         "bill_zip": "27536",
#         "bill_npi": "**********",
#         "bill_id": "",
#         "bill_phone": "**********",
#         "bill_taxid": "*********",
#         "bill_taxid_type": "E",
        
#         # Additional claim details:
#         "diag_ref_1": "A",
#         "units_1": 1,
        
#         # Patient (recipient) information:
#         "pat_name_f": client_record.get("firstname", "") if client_record else "",
#         "pat_name_l": client_record.get("lastname", "") if client_record else "",
#         "pat_addr_1": client_record.get("streetAddress", "") if client_record else "",
#         "pat_city": client_record.get("city", "") if client_record else "",
#         "pat_state": convert_state(client_record.get("state", "")) if client_record else "",
#         "pat_zip": client_record.get("zipCode", "") if client_record else "",
#         "pat_dob": format_date(client_record.get("dateOfBirth", "")) if client_record else "",
#         "pat_sex": convert_sex(client_record.get("gender", "")) if client_record else ""
#     }
    
#     # (Optionally, you may also create a service line structure)
#     claimInformation = {
#         "serviceLines": [
#             {
#                 "professionalService": {
#                     "procedureModifiers": procedure_modifiers
#                 }
#             }
#         ]
#     }
    
#     # Log the post-session claim data to the postsessionclaims collection.
#     # Logging client and therapist first/last names, session date, and meetingId.
#     log_record = {
#         "clientId": client_oid,                       # Client's ObjectId as string (or ObjectId if desired)
#         "therapistId": therapist_oid,                 # Therapist's ObjectId as string (or ObjectId if desired)
#         "session_date": meeting_record["createdAt"]["$date"],  # Session date from meeting_record
#         "meetingId": meeting_record.get("meetingId", "")
# }
#     postsessionclaims_collection.insert_one(log_record)
    
#     # Create a CSV file from the payload using the helper function.
#     output_file_path = '/tmp/output.csv'   # In AWS Lambda, use /tmp/ for temporary file storage.
#     create_csv_from_payload(payload, output_file_path)
#     print(f"CSV file created at {output_file_path}")
    
#     # Prepare to upload the CSV file via a multipart/form-data POST request.
#     account_key = os.environ.get("CLAIM_MD_ACCOUNT_KEY", "YOUR_ACCOUNT_KEY_HERE")
#     upload_url = "https://svc.claim.md/services/upload/"
#     upload_headers = {
#         "Accept": "application/xml"
#     }
    
#     with open(output_file_path, 'rb') as file:
#         files = {
#             'AccountKey': (None, account_key),
#             'File': (output_file_path, file, 'text/csv')
#         }
#         response = requests.post(upload_url, files=files, headers=upload_headers)
    
#     # Check the response; we expect to see "Received 1 claims in file: output.csv"
#     expected_message = "Received 1 claims in file: output.csv"
#     if expected_message in response.text:
#         print("Claim submitted successfully. Response:", response.text)
#         submission_status = "Success"
#     else:
#         print("Claim submission failed. Response:", response.text)
#         submission_status = "Failure"
    
#     # Update the log record (for example, using meetingId as a key) with the submission status and response.
#     postsessionclaims_collection.update_one(
#         {"meetingId": meeting_record.get("meetingId", "")},
#         {"$set": {"submission_status": submission_status, "response": response.text}},
#         upsert=True
#     )
    
#     return {
#         'statusCode': 200,
#         'body': json.dumps({
#             'submission_status': submission_status,
#             'response': response.text
#         })
#     }

import os
import json
import random
import string
import requests
import csv
from datetime import datetime
from pymongo import MongoClient
from bson.objectid import ObjectId

# --- Helpers ------------------------------------------------------------------

def generateRandomKey(length):
    characters = string.ascii_uppercase + string.digits
    return ''.join(random.choices(characters, k=length))

def format_date(date_obj, output_format="%m/%d/%Y"):
    if isinstance(date_obj, datetime):
        return date_obj.strftime(output_format)
    try:
        dt = datetime.fromisoformat(date_obj.replace("Z", ""))
        return dt.strftime(output_format)
    except Exception:
        return date_obj or ""

def convert_sex(sex_value):
    if not sex_value:
        return ""
    sv = sex_value.strip().lower()
    if sv == "male":
        return "M"
    if sv == "female":
        return "F"
    return sv.upper()

STATE_MAP = {
    "Alabama":"AL","Alaska":"AK","Arizona":"AZ","Arkansas":"AR","California":"CA",
    "Colorado":"CO","Connecticut":"CT","Delaware":"DE","Florida":"FL","Georgia":"GA",
    "Hawaii":"HI","Idaho":"ID","Illinois":"IL","Indiana":"IN","Iowa":"IA","Kansas":"KS",
    "Kentucky":"KY","Louisiana":"LA","Maine":"ME","Maryland":"MD","Massachusetts":"MA",
    "Michigan":"MI","Minnesota":"MN","Mississippi":"MS","Missouri":"MO","Montana":"MT",
    "Nebraska":"NE","Nevada":"NV","New Hampshire":"NH","New Jersey":"NJ","New Mexico":"NM",
    "New York":"NY","North Carolina":"NC","North Dakota":"ND","Ohio":"OH","Oklahoma":"OK",
    "Oregon":"OR","Pennsylvania":"PA","Rhode Island":"RI","South Carolina":"SC",
    "South Dakota":"SD","Tennessee":"TN","Texas":"TX","Utah":"UT","Vermont":"VT",
    "Virginia":"VA","Washington":"WA","West Virginia":"WV","Wisconsin":"WI","Wyoming":"WY"
}

def convert_state(full_state):
    if not full_state:
        return ""
    return STATE_MAP.get(full_state.strip(), full_state.strip())

def create_csv_from_payload(payload, output_file):
    with open(output_file, 'w', newline='') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=list(payload.keys()))
        writer.writeheader()
        writer.writerow(payload)
    return output_file

# --- Lambda Handler -----------------------------------------------------------

def lambda_handler(event, context):
    # 1. Extract meeting_record from event
    if 'body' in event:
        try:
            data = json.loads(event['body'])
        except:
            return {'statusCode':400,'body':json.dumps({'message':'Invalid JSON'})}
        meeting_record = data.get('meeting_record', data)
    elif 'meeting_record' in event:
        meeting_record = event['meeting_record']
    elif all(k in event for k in ['_id','serviceProvided','therapistId']):
        meeting_record = event
    else:
        return {'statusCode':400,'body':json.dumps({'message':'No meeting_record found'})}

    # 2. Parse key fields
    claim_number   = meeting_record["_id"]["$oid"]
    proc_code      = meeting_record.get("serviceProvided","")
    from_date      = format_date(meeting_record["createdAt"]["$date"], "%m-%d-%y")
    client_oid     = meeting_record["clientId"]["$oid"]
    therapist_oid  = meeting_record["therapistId"]["$oid"]
    client_id      = ObjectId(client_oid)
    therapist_id   = ObjectId(therapist_oid)

    # 3. Connect to MongoDB
    MONGO_URI = os.environ.get('MONGO_URI','mongodb://localhost:27017')
    DB_NAME   = os.environ.get('DB_NAME','your_database_name')
    db        = MongoClient(MONGO_URI)[DB_NAME]

    users_col             = db['users']
    insurance_col         = db['insurances']
    insurance_comp_col    = db['insurancecompanies']
    diagnosis_col         = db['diagnosisnotes']
    postsessionclaims_col = db['postsessionclaims']

    # 4. Diagnosis codes
    diag_1 = diag_2 = ""
    for rec in diagnosis_col.find({"clientId":client_id}):
        codes = rec.get("diagnosisICDcodes",[])
        if codes:
            diag_1 = codes[0].get("value","").replace(".","")
            sec = rec.get("secondaryDiagnosisICDcodes",[])
            if sec:
                diag_2 = sec[0].get("value","").replace(".","")
            break

    # 5. User records
    client_rec   = users_col.find_one({"_id":client_id}) or {}
    provider_rec = users_col.find_one({"_id":therapist_id}) or {}

    # 6. Insurance via insuranceId field on user
    raw_ins_id = client_rec.get("insuranceId")
    if isinstance(raw_ins_id, dict) and "$oid" in raw_ins_id:
        ins_id_str = raw_ins_id["$oid"]
    elif isinstance(raw_ins_id, ObjectId):
        ins_id_str = str(raw_ins_id)
    else:
        ins_id_str = None

    insurance_rec = {}
    if ins_id_str:
        try:
            insurance_rec = insurance_col.find_one({"_id":ObjectId(ins_id_str)}) or {}
        except Exception as e:
            print("Invalid insuranceId:", ins_id_str, e)

    # 7. Subscriber & address details
    subscriber = insurance_rec.get("subscriber",{})
    member_id  = subscriber.get("memberId","")
    sub_dob    = subscriber.get("dateOfBirth","")
    addr       = subscriber.get("address",{})
    ins_addr1  = addr.get("address1","")
    ins_city   = addr.get("city","")
    ins_state  = addr.get("state","")
    ins_postal = addr.get("postalCode","")

    # 8. Insurance company lookup
    comp_id = insurance_rec.get("insuranceCompanyId")
    comp_rec = {}
    if comp_id:
        try:
            comp_rec = insurance_comp_col.find_one({"_id":comp_id}) or {}
        except:
            comp_rec = {}
    organization_name       = comp_rec.get("organizationName","")
    trading_partner_service = comp_rec.get("tradingPartnerServiceId","")

    # 9. Place of service & modifiers
    org_norm = organization_name.strip()
    if org_norm in ["Alliance Health","United Health Care Community Plan", 'Trillium Health Resources', 
    'ALLIANCE HEALTH', 'Partners Behavioral Health Management','Vaya Health']:
        place_of_service = "11"
        modifier = ""
        proc_mods = [""]
    else:
        place_of_service = "10"
        modifier = "95"
        proc_mods = ["95"]

    # 10. Build payload
    pcn_key = generateRandomKey(20)
    payload = {
        "claim_form":1500,
        "payer_name":organization_name,
        "payerid":trading_partner_service,
        "accept_assign":"Y",
        "employment_related":"N",
        "ins_name_f":client_rec.get("firstname",""),
        "ins_name_l":client_rec.get("lastname",""),
        "ins_addr_1":ins_addr1,
        "ins_city":ins_city,
        "ins_dob":format_date(sub_dob),
        "ins_sex":convert_sex(client_rec.get("gender","")),
        "ins_state":convert_state(ins_state),
        "ins_zip":ins_postal,
        "ins_number":member_id,
        "bill_taxonomy":"251S00000X",
        "place_of_service_1":place_of_service,
        "prov_name_f":provider_rec.get("firstname",""),
        "prov_name_l":provider_rec.get("lastname",""),
        "prov_npi":provider_rec.get("nPI1",""),
        "prov_taxonomy":provider_rec.get("taxonomyCode",""),
        "pcn":pcn_key,
        "charge_1":180,
        "pat_rel":"18",
        "total_charge":180,
        "claimNumber":claim_number,
        "proc_code_1":proc_code,
        "mod1_1":modifier,
        "diag_1":diag_1,
        "diag_2":diag_2,
        "from_date_1":from_date,
        "bill_name":"Lavni Inc",
        "bill_addr_1":"804 South Garnett St.",
        "bill_addr_2":"",
        "bill_city":"Henderson",
        "bill_state":"NC",
        "bill_zip":"27536",
        "bill_npi":"**********",
        "bill_id":"",
        "bill_phone":"**********",
        "bill_taxid":"*********",
        "bill_taxid_type":"E",
        "diag_ref_1":"A",
        "units_1":1,
        "pat_name_f":client_rec.get("firstname",""),
        "pat_name_l":client_rec.get("lastname",""),
        "pat_addr_1":client_rec.get("streetAddress",""),
        "pat_city":client_rec.get("city",""),
        "pat_state":convert_state(client_rec.get("state","")),
        "pat_zip":client_rec.get("zipCode",""),
        "pat_dob":format_date(client_rec.get("dateOfBirth","")),
        "pat_sex":convert_sex(client_rec.get("gender",""))
    }
    print(payload)
    # 11. Log session claim
    postsessionclaims_col.insert_one({
        "clientId": client_oid,
        "therapistId": therapist_oid,
        "session_date": meeting_record["createdAt"]["$date"],
        "meetingId": meeting_record.get("meetingId","")
    })

    # 12. Write CSV
    out_path = '/tmp/output.csv'
    create_csv_from_payload(payload, out_path)

    # 13. Submit to Claim.MD
    account_key = os.environ.get("CLAIM_MD_ACCOUNT_KEY","")
    url = "https://svc.claim.md/services/upload/"
    with open(out_path,'rb') as f:
        files = {
            'AccountKey': (None, account_key),
            'File': (out_path, f, 'text/csv')
        }
        resp = requests.post(url, files=files, headers={"Accept":"application/xml"})

    text = resp.text or ""
    status = "Success" if "Received 1 claims in file:" in text else "Failure"

    # 14. Update log with result
    postsessionclaims_col.update_one(
        {"meetingId": meeting_record.get("meetingId","")},
        {"$set": {"submission_status":status,"response":text}},
        upsert=True
    )

    return {
        'statusCode':200,
        'body':json.dumps({'submission_status':status,'response':text})
    }